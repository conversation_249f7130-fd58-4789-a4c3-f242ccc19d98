import React, { useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import {  notification, TableService, ConfirmationService } from '@/services';

const Allsalarystructure = () => {
  // Global search state
  const [globalFilter, setGlobalFilter] = useState('');
  const [allsalarystructure, setAllsalarystructure] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // Server pagination state
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch all salarystructure on component mount and when pagination/filters change
  useEffect(() => {
    fetchAllsalarystructure();
  }, [pagination.pageIndex, pagination.pageSize, globalFilter]);
  
  const fetchAllsalarystructure = async () => {
    setLoading(true);
    
    const salarystructure = await salarystructureearch.getAllsalarystructure(
      globalFilter || "",
      pagination.pageIndex + 1, // API expects 1-based page numbers
      pagination.pageSize,
      null, // obStatus
      null  // isActive
    );
    
    if (salarystructure && salarystructure.Data) {
      // Map API data to table format
      const mappedsalarystructure = salarystructure.Data.map(ss => ({
        DepartmentName: ss.DepartmentName, // Format as EMP001, EMP002, etc.
        PositionName: ss.PositionName,
        BasicSalary: ss.BasicSalary,
        HRA: ss.HRA,
        DA: ss.DA,
        HourlyRate: ss.HourlyRate,
        OtherAllowances: emp.OtherAllowances,
        GrossSalary: emp.GrossSalary,
        ProvidentFund: emp.ProvidentFund,
        ProfessionalTax: emp.ProfessionalTax,
        IncomeTax: emp.IncomeTax,
        OtherDeductions: emp.OtherDeductions,
        TotalDeductions: emp.TotalDeductions,
        NetSalary: emp.NetSalary
      }));
      
      setAllsalarystructure(mappedsalarystructure);
      
      // Set pagination info from API response
      setTotalItems(salarystructure.TotalRecords || salarystructure.Total || mappedsalarystructure.length);
      setTotalPages(salarystructure.TotalPages || Math.ceil((salarystructure.TotalRecords || salarystructure.Total || mappedsalarystructure.length) / pagination.pageSize));
      setCurrentPage(pagination.pageIndex + 1);
      
      console.log('Mapped salarystructure:', mappedsalarystructure);
      console.log('Pagination info:', { 
        totalItems: salarystructure.TotalRecords || salarystructure.Total, 
        totalPages: salarystructure.TotalPages,
        currentPage: pagination.pageIndex + 1 
      });
    } else {
      setAllsalarystructure([]);
      setTotalItems(0);
      setTotalPages(0);
      setCurrentPage(1);
    }
    
    setLoading(false);
  };

  // Handle pagination changes from table
  const handlePaginationChange = (newPagination) => {
    setPagination(newPagination);
  };

  // Handle global filter changes with debouncing
  const handleGlobalFilterChange = (value) => {
    setGlobalFilter(value);
    // Reset to first page when searching
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  // Enhanced table columns configuration with all features enabled
  const columns = useMemo(() => [
    {
      accessorKey: 'empID',
      header: 'Employee ID',
      enableSorting: true,
      enableColumnFilter: true,
      size: 120,
    },
    {
      accessorKey: 'name',
      header: 'DepartmentName',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <Link
          to={`/dashboard/all-salarystructure/${row.original.id}`}
          className="text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors"
        >
          {row.original.name}
        </Link>
      ),
      size: 180,
    },
    {
      accessorKey: 'email',
      header: 'PositionName',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <a
          href={`mailto:${row.original.email}`}
          className="text-gray-700 hover:text-blue-600 transition-colors"
        >
          {row.original.email}
        </a>
      ),
      size: 200,
    },
    {
      accessorKey: 'phoneNumber',
      header: 'GrossSalary',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <a
          href={`tel:${row.original.phoneNumber}`}
          className="text-gray-700 hover:text-blue-600 transition-colors"
        >
          {row.original.phoneNumber}
        </a>
      ),
      size: 140,
    },
    {
      accessorKey: 'gender',
      header: 'NetSalary',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          {row.original.gender}
        </span>
      ),
      size: 100,
    },
    {
      accessorKey: 'status',
      header: 'BasicSalary',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => {
        const status = row.original.status;
        const statusColors = {
          'Active': 'bg-green-100 text-green-800',
          'On Leave': 'bg-yellow-100 text-yellow-800',
          'Inactive': 'bg-red-100 text-red-800'
        };
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
            {status}
          </span>
        );
      },
      size: 100,
    },
    {
      accessorKey: 'joinDate',
      header: 'ProvidentFund',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => {
        const date = new Date(row.original.joinDate);
        return date.toLocaleDateString('en-IN', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      },
      size: 120,
    },
    {
      accessorKey: 'address',
      header: 'HRA',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <span className="truncate max-w-xs" title={row.original.address}>
          {row.original.address}
        </span>
      ),
      size: 200,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'DA',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'OtherDeductions',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'TotalDeductions',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'OtherAllowances',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'ProfessionalTax',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'IncomeTax',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
    {
      accessorKey: 'emergencyContact',
      header: 'OtherDeductions',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.emergencyContact}</div>
          <div className="text-gray-500">{row.original.emergencyContactNumber}</div>
        </div>
      ),
      size: 160,
    },
  ], []);

  return (
    <div className="p-6">
      <div className="">
        <TableService
          columns={columns}
          data={allsalarystructure}
          initialPageSize={5}
          serverPagination={true}
          pageCount={totalPages}
          totalItems={totalItems}
          totalPages={totalPages}
          currentPage={currentPage}
          onPaginationChange={handlePaginationChange}
          loading={loading}
          globalFilter={globalFilter}
          onGlobalFilterChange={handleGlobalFilterChange}
        />
      </div>
    </div>
  );
};

export default Allsalarystructure;