import React, { useState } from 'react';
import { FaMoneyCheckAlt, FaClipboardList, FaCheckCircle, FaHistory } from 'react-icons/fa';

export default function LoanAdvanceManagement() {
  const [form, setForm] = useState({ type: 'Loan', amount: '', reason: '' });
  const [status, setStatus] = useState('Not Submitted');

  const handleSubmit = () => {
    setStatus('Pending Approval');
  };

  return (
    <div className="p-6 md:p-10 bg-gradient-to-br from-gray-100 to-white min-h-screen">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">💼 Loans & Advances Management</h1>

      {/* Request Form */}
      <div className="bg-white shadow-lg p-6 rounded-lg mb-10 transition hover:shadow-xl">
        <div className="flex items-center gap-2 mb-4">
          <FaMoneyCheckAlt className="text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-800">Request Loan/Advance</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <label className="block">
            <span className="text-gray-700">Type</span>
            <select
              value={form.type}
              onChange={(e) => setForm({ ...form, type: e.target.value })}
              className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Loan">Loan</option>
              <option value="Advance">Advance</option>
            </select>
          </label>

          <label className="block">
            <span className="text-gray-700">Amount</span>
            <input
              type="number"
              value={form.amount}
              onChange={(e) => setForm({ ...form, amount: e.target.value })}
              placeholder="Enter amount"
              className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </label>

          <label className="block col-span-2">
            <span className="text-gray-700">Reason</span>
            <textarea
              value={form.reason}
              onChange={(e) => setForm({ ...form, reason: e.target.value })}
              placeholder="Enter reason"
              rows="3"
              className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </label>
        </div>
        <button
          onClick={handleSubmit}
          className="mt-6 bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded shadow"
        >
          Submit Request
        </button>
      </div>

      {/* Eligibility Section */}
      <div className="bg-white shadow-lg p-6 rounded-lg mb-10 transition hover:shadow-xl">
        <div className="flex items-center gap-2 mb-4">
          <FaClipboardList className="text-green-600" />
          <h2 className="text-xl font-semibold text-gray-800">Eligibility Criteria</h2>
        </div>
        <ul className="list-disc pl-5 text-gray-700 space-y-1">
          <li>Minimum tenure: 6 months</li>
          <li>Maximum loan: Up to 3x monthly salary</li>
          <li>Only one active loan at a time</li>
        </ul>
      </div>

      {/* Request Status */}
      <div className="bg-white shadow-lg p-6 rounded-lg mb-10 transition hover:shadow-xl">
        <div className="flex items-center gap-2 mb-4">
          <FaCheckCircle className="text-purple-600" />
          <h2 className="text-xl font-semibold text-gray-800">Request Status</h2>
        </div>
        <p className="text-gray-700 text-lg">
          Current Status:{" "}
          <span
            className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
              status === 'Pending Approval'
                ? 'bg-yellow-100 text-yellow-800'
                : status === 'Approved'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {status}
          </span>
        </p>
      </div>

      {/* Repayment Info */}
      <div className="bg-white shadow-lg p-6 rounded-lg transition hover:shadow-xl">
        <div className="flex items-center gap-2 mb-4">
          <FaHistory className="text-red-600" />
          <h2 className="text-xl font-semibold text-gray-800">Repayment Status</h2>
        </div>
        <p className="text-gray-800 text-md mb-1">
          <strong>Outstanding Balance:</strong> ₹10,000
        </p>
        <p className="text-gray-800 text-md mb-3">
          <strong>Next EMI:</strong> ₹1,000 on <span className="text-blue-600 font-medium">1st Aug 2025</span>
        </p>
        <div>
          <p className="text-gray-700 mb-2 font-semibold">Repayment History:</p>
          <ul className="list-disc pl-5 text-gray-600 text-sm space-y-1">
            <li>1st Jul 2025 - ₹1,000 - <span className="text-green-600">Paid</span></li>
            <li>1st Jun 2025 - ₹1,000 - <span className="text-green-600">Paid</span></li>
          </ul>
        </div>
      </div>
    </div>
  );
}
