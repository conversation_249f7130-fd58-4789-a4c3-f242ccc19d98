import React, { useState } from 'react';
import { ArrowLeft, Building, Save, Plus, X, Users } from 'lucide-react';
import createDepartment, { validateDepartmentData } from '@/services/Departments/createDepartments';
import notification from '@/services/NotificationService';

const AddDepartment = () => {
    const [formData, setFormData] = useState({
        deptName: '',
        positions: []
    });
    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [newPosition, setNewPosition] = useState('');
    const [positionError, setPositionError] = useState('');

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
    };

    const handleAddPosition = () => {
        const trimmedPosition = newPosition.trim();

        if (!trimmedPosition) {
            setPositionError('Position name is required');
            return;
        }
        if (trimmedPosition.length < 2 || trimmedPosition.length > 50) {
            setPositionError('Position name must be 2-50 characters long');
            return;
        }
        if (formData.positions.some(pos => pos.toLowerCase() === trimmedPosition.toLowerCase())) {
            setPositionError('This position already exists');
            return;
        }

        setFormData(prev => ({ ...prev, positions: [...prev.positions, trimmedPosition] }));
        setNewPosition('');
        setPositionError('');
    };

    const handleRemovePosition = (index) => {
        setFormData(prev => ({ ...prev, positions: prev.positions.filter((_, i) => i !== index) }));
    };

    const handlePositionInputChange = (e) => {
        setNewPosition(e.target.value);
        if (positionError) setPositionError('');
    };

    const validateForm = () => {
        // Use the service validation function
        const validation = validateDepartmentData(formData);
        
        if (!validation.isValid) {
            const newErrors = {};
            validation.errors.forEach(error => {
                if (error.includes('Department name')) {
                    newErrors.deptName = error;
                } else if (error.includes('Position')) {
                    newErrors.positions = error;
                }
            });
            setErrors(newErrors);
            return false;
        }
        
        setErrors({});
        return true;
    };

    const handleSubmit = async (e) => {
        if (e) e.preventDefault();
        
        if (!validateForm()) {
            notification().error('Please fix the errors before submitting');
            return;
        }

        setIsSubmitting(true);
        notification().loading('Adding department...');

        try {
            // Call the service to create department and positions
            const result = await createDepartment({
                deptName: formData.deptName.trim(),
                positions: formData.positions
            });

            console.log('Department creation result:', result);
            
            // Show success message based on positions created
            let successMessage;
            if (result.totalPositionsRequested > 0) {
                if (result.totalPositionsCreated === result.totalPositionsRequested) {
                    successMessage = `Department "${formData.deptName.trim()}" with ${result.totalPositionsCreated} positions added successfully!`;
                } else {
                    successMessage = `Department "${formData.deptName.trim()}" added successfully! ${result.totalPositionsCreated} of ${result.totalPositionsRequested} positions were created.`;
                }
            } else {
                successMessage = `Department "${formData.deptName.trim()}" added successfully!`;
            }
            
            notification().success(successMessage);

            // Reset form
            setFormData({ deptName: '', positions: [] });
            setErrors({});
            setNewPosition('');
            setPositionError('');

        } catch (error) {
            console.error('Error adding department:', error);
            notification().error(error.message || 'Failed to add department. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Add Department</h1>
                    <p className="text-gray-600 text-sm sm:text-base">Create a new department and optionally define available positions</p>
                </div>
                <button
                    onClick={() => window.history.back()}
                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors text-sm"
                >
                    <ArrowLeft size={16} />
                    Back to Departments
                </button>
            </div>

            {/* Form */}
            <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
                <div onSubmit={handleSubmit} className="space-y-6">
                    {/* Department Section */}
                    <div className="grid md:grid-cols-2 gap-6">
                        <div className="md:col-span-2">
                            <div className="flex items-center gap-2 mb-4">
                                <Building className="text-blue-600" size={20} />
                                <h2 className="text-lg font-semibold text-gray-900">Department Information</h2>
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Department Name <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                name="deptName"
                                value={formData.deptName}
                                onChange={handleInputChange}
                                placeholder="e.g., Human Resources, Engineering"
                                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.deptName ? 'border-red-300' : 'border-gray-300'}`}
                                disabled={isSubmitting}
                            />
                            {errors.deptName && <p className="mt-1 text-sm text-red-600">{errors.deptName}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Add Position <span className="text-gray-400">(Optional)</span>
                            </label>
                            <div className="flex gap-2">
                                <input
                                    type="text"
                                    value={newPosition}
                                    onChange={handlePositionInputChange}
                                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddPosition())}
                                    placeholder="e.g., Software Engineer"
                                    className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${positionError ? 'border-red-300' : 'border-gray-300'}`}
                                    disabled={isSubmitting}
                                />
                                <button
                                    type="button"
                                    onClick={handleAddPosition}
                                    disabled={!newPosition.trim() || isSubmitting}
                                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-1"
                                >
                                    <Plus size={14} />
                                    <span className="hidden sm:inline">Add</span>
                                </button>
                            </div>
                            {positionError && <p className="mt-1 text-sm text-red-600">{positionError}</p>}
                        </div>
                    </div>

                    {/* Positions List */}
                    <div>
                        <div className="flex items-center gap-2 mb-3">
                            <Users className="text-green-600" size={20} />
                            <label className="text-lg font-semibold text-gray-900">
                                Positions ({formData.positions.length})
                            </label>
                            <span className="text-sm text-gray-500">(Optional)</span>
                        </div>

                        {formData.positions.length === 0 ? (
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <Users className="mx-auto h-10 w-10 text-gray-400 mb-2" />
                                <p className="text-gray-500 text-sm">No positions added yet</p>
                                <p className="text-xs text-gray-400">You can add positions now or later</p>
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 max-h-48 overflow-y-auto border rounded-lg p-3 bg-gray-50">
                                {formData.positions.map((position, index) => (
                                    <div key={index} className="flex items-center justify-between bg-white px-3 py-2 rounded border text-sm">
                                        <span className="flex-1 mr-2 truncate" title={position}>{position}</span>
                                        <button
                                            type="button"
                                            onClick={() => handleRemovePosition(index)}
                                            disabled={isSubmitting}
                                            className="text-red-500 hover:text-red-700 disabled:opacity-50"
                                            title="Remove position"
                                        >
                                            <X size={14} />
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Submit Section */}
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-3 pt-4 border-t">
                        <button
                            onClick={() => window.history.back()}
                            className="w-full sm:w-auto px-4 py-2 text-center text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors order-2 sm:order-1"
                        >
                            Cancel
                        </button>
                        <button
                            type="button"
                            onClick={handleSubmit}
                            disabled={isSubmitting || !formData.deptName.trim()}
                            className={`w-full sm:w-auto flex items-center justify-center gap-2 px-6 py-2 rounded-lg font-medium transition-all order-1 sm:order-2 ${isSubmitting || !formData.deptName.trim()
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                                }`}
                        >
                            {isSubmitting ? (
                                <>
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <Save size={16} />
                                    Add Department
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* Tips */}
            <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Quick Tips</h3>
                <div className="text-xs text-blue-800 space-y-1">
                    <p className='space-x-5 font-bold'>
                        <span>
                            • Use clear, descriptive names
                        </span>
                        <span>
                            • Avoid abbreviations
                        </span>
                        <span>
                            • Keep names consistent
                        </span>
                        <span>
                            • Department names should be unique
                        </span>
                        <span>
                            • Positions can be added later
                        </span>
                        <span>
                            • You can edit positions anytime
                        </span>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default AddDepartment;