import React, { useState } from 'react';
import { FaEdit, FaMoneyBillWave, FaCogs } from 'react-icons/fa';

export default function LoanAdminDashboard() {
  const [repaymentConfig, setRepaymentConfig] = useState({
    emi: '',
    startDate: '',
    installments: '',
    interest: '',
  });

  // Sample data: Replace with real backend data
  const loans = [
    {
      id: 1,
      employee: '<PERSON>',
      type: 'Loan',
      amount: 15000,
      status: 'Active',
      outstanding: 5000,
    },
    {
      id: 2,
      employee: '<PERSON>',
      type: 'Advance',
      amount: 8000,
      status: 'Active',
      outstanding: 2000,
    },
  ];

  const handleConfigChange = (e) => {
    const { name, value } = e.target;
    setRepaymentConfig({ ...repaymentConfig, [name]: value });
  };

  const calculateWithInterest = (amount, rate, months) => {
    if (!rate || rate === '0') return amount / months;
    const interestAmount = (amount * (rate / 100));
    return ((amount + interestAmount) / months).toFixed(2);
  };

  return (
    <div className="p-6 md:p-10 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">📊 HR/Finance Loan Management</h1>

      {/* Section 1: Active Loans Tracker */}
      <div className="bg-white p-6 rounded shadow-lg mb-10">
        <div className="flex items-center gap-2 mb-4">
          <FaMoneyBillWave className="text-green-600" />
          <h2 className="text-xl font-semibold text-gray-800">Active Loans & Advances</h2>
        </div>
        <table className="w-full text-left mt-4 border-collapse">
          <thead className="bg-gray-200">
            <tr>
              <th className="p-2">Employee</th>
              <th className="p-2">Type</th>
              <th className="p-2">Amount</th>
              <th className="p-2">Outstanding</th>
              <th className="p-2">Status</th>
              <th className="p-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loans.map((loan) => (
              <tr key={loan.id} className="border-t hover:bg-gray-50">
                <td className="p-2">{loan.employee}</td>
                <td className="p-2">{loan.type}</td>
                <td className="p-2">₹{loan.amount}</td>
                <td className="p-2">₹{loan.outstanding}</td>
                <td className="p-2">
                  <span className="px-2 py-1 text-sm rounded bg-yellow-100 text-yellow-800">
                    {loan.status}
                  </span>
                </td>
                <td className="p-2">
                  <button className="text-blue-600 hover:text-blue-800">
                    <FaEdit />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Section 2: Repayment Configurator */}
      <div className="bg-white p-6 rounded shadow-lg">
        <div className="flex items-center gap-2 mb-4">
          <FaCogs className="text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-800">Configure Repayment Schedule</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <label className="block">
            <span className="text-gray-700">EMI Amount</span>
            <input
              type="number"
              name="emi"
              value={repaymentConfig.emi}
              onChange={handleConfigChange}
              className="mt-1 block w-full border rounded px-3 py-2 focus:ring-blue-500 focus:outline-none"
              placeholder="Enter fixed EMI amount"
            />
          </label>
          <label className="block">
            <span className="text-gray-700">Start Date</span>
            <input
              type="date"
              name="startDate"
              value={repaymentConfig.startDate}
              onChange={handleConfigChange}
              className="mt-1 block w-full border rounded px-3 py-2 focus:ring-blue-500 focus:outline-none"
            />
          </label>
          <label className="block">
            <span className="text-gray-700">Installments</span>
            <input
              type="number"
              name="installments"
              value={repaymentConfig.installments}
              onChange={handleConfigChange}
              className="mt-1 block w-full border rounded px-3 py-2 focus:ring-blue-500 focus:outline-none"
              placeholder="Number of months"
            />
          </label>
          <label className="block">
            <span className="text-gray-700">Interest Rate (%)</span>
            <input
              type="number"
              name="interest"
              value={repaymentConfig.interest}
              onChange={handleConfigChange}
              className="mt-1 block w-full border rounded px-3 py-2 focus:ring-blue-500 focus:outline-none"
              placeholder="e.g. 5"
            />
          </label>
        </div>

        {/* EMI Calculation Preview */}
        {repaymentConfig.emi || repaymentConfig.interest ? (
          <div className="mt-6 bg-blue-50 p-4 rounded text-sm text-blue-800">
            Estimated EMI:
            <strong className="ml-2">
              ₹{calculateWithInterest(10000, Number(repaymentConfig.interest), Number(repaymentConfig.installments))}
            </strong>{' '}
            for a ₹10,000 loan (example)
          </div>
        ) : null}

        <button className="mt-6 bg-blue-600 text-white px-5 py-2 rounded hover:bg-blue-700">
          Save Repayment Settings
        </button>
      </div>
    </div>
  );
}
