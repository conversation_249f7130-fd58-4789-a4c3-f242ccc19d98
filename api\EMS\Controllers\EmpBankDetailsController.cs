﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpBankDetailsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpBankDetailsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpBankDetails
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpBankDetail>>> GetEmpBankDetails()
        {
            return await _context.EmpBankDetails.ToListAsync();
        }

        // GET: api/EmpBankDetails/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpBankDetail>> GetEmpBankDetail(int id)
        {
            var empBankDetail = await _context.EmpBankDetails.FindAsync(id);

            if (empBankDetail == null)
            {
                return NotFound();
            }

            return empBankDetail;
        }

        // PUT: api/EmpBankDetails/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpBankDetail(int id, EmpBankDetail empBankDetail)
        {
            if (id != empBankDetail.EbdID)
            {
                return BadRequest();
            }

            _context.Entry(empBankDetail).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpBankDetailExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpBankDetails
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpBankDetail>> PostEmpBankDetail(EmpBankDetail empBankDetail)
        {
            // Debug: Log the received data
            Console.WriteLine($"Received EmpBankDetail: EmpID={empBankDetail?.EmpID}, BankName={empBankDetail?.BankName}");

            if (empBankDetail == null)
            {
                return BadRequest("EmpBankDetail data is null.");
            }

            _context.EmpBankDetails.Add(empBankDetail);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpBankDetail", new { id = empBankDetail.EbdID }, empBankDetail);
        }

        // DELETE: api/EmpBankDetails/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpBankDetail(int id)
        {
            var empBankDetail = await _context.EmpBankDetails.FindAsync(id);
            if (empBankDetail == null)
            {
                return NotFound();
            }

            _context.EmpBankDetails.Remove(empBankDetail);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpBankDetailExists(int id)
        {
            return _context.EmpBankDetails.Any(e => e.EbdID == id);
        }
    }
}
