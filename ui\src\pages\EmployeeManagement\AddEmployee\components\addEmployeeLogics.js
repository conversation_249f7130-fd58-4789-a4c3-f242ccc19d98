// addEmployeeLogic.js
import notification from '@/services/NotificationService';

const notify = notification();
// Step configuration

export const stepConfig = {
  1: { 
    title: 'Basic Information',
    icon: 'fas fa-user',
    fields: [
      { name: 'firstName', weight: 1 },
      // { name: 'middleName', weight: 0.5 },
      { name: 'lastName', weight: .5 },
      { name: 'personalEmail', weight: 1 },
      { name: 'workEmail', weight: 1 },
      { name: 'phoneNumber', weight: 1 },
      { name: 'address', weight: 1 },
      { name: 'emergencyContact', weight: 1 },
      { name: 'emergencyContactNumber', weight: 1 },
      { name: 'dob', weight: 0.5 },
      { name: 'gender', weight: 1 },
      { name: 'doj', weight: 0.5 },
      { name: 'profilePicture', weight: 0.5 },
    ],
  },
  2: { 
    title: 'Posting Details',
    icon: 'fas fa-briefcase',
    fields: [
      { name: 'department', weight: 2 },
      { name: 'position', weight: 3 },
    ],
  },
  3: { 
    title: 'Bank Details',
    icon: 'fas fa-university',
    fields: [
      { name: 'bankName', weight: 1.5 },
      { name: 'accountNumber', weight: 1.5 },
      { name: 'ifscCode', weight: 1.5 },
      { name: 'branchName', weight: 1.5 },
      { name: 'cancelledCheque', weight: 1.5 },
    ],
  },
  4: { 
    title: 'Academic Details',
    icon: 'fas fa-graduation-cap',
    fields: [{ name: 'degrees', weight: 7.5 }],
  }
};

// Initial form data
export const initialFormData = {
  firstName: '',
  // middleName: '',
  lastName: '',
  personalEmail: '',
  workEmail: '',
  phoneNumber: '',
  address: '',
  emergencyContact: '',
  emergencyContactNumber: '',
  dob: '',
  gender: '',
  doj: '',
  obsStatus: 'pending', // Default value
  isActive: false, // Default value
  profilePicture: null,
  bankName: '',
  accountNumber: '',
  ifscCode: '',
  branchName: '',
  cancelledCheque: null,
  degrees: [{ degree: '', institution: '', yearOfPassing: '', grade: '' }],
  department: '',
  position: '',
};

// Calculate completion percentage
export const calculateCompletion = (formData) => {
  let totalWeight = 0;
  let completedWeight = 0;

  // console.log('=== COMPLETION CALCULATION DEBUG ===');
  // console.log('Current formData:', formData);

  Object.values(stepConfig).forEach((step, stepIndex) => {
    // console.log(`\nStep ${stepIndex + 1}: ${step.title}`);

    step.fields.forEach((field) => {
      totalWeight += field.weight;
      let isCompleted = false;

      if (field.name === 'degrees') {
        // Handle both array structure and individual fields
        if (formData.degrees && Array.isArray(formData.degrees)) {
          // Original array structure
          const hasCompleteDegree = formData.degrees.some((degree) => {
            const degreeFields = ['degree', 'institution', 'yearOfPassing', 'grade'];
            return degreeFields.every((fieldName) =>
              degree[fieldName] && degree[fieldName].toString().trim() !== ''
            );
          });
          isCompleted = hasCompleteDegree;
        } else {
          // Individual fields structure (for AddEmployee.jsx)
          const degreeFields = ['degree', 'institution', 'yearOfPassing', 'grade'];
          isCompleted = degreeFields.every((fieldName) =>
            formData[fieldName] && formData[fieldName].toString().trim() !== ''
          );
        }

        // console.log(`  ${field.name} (weight: ${field.weight}): ${isCompleted ? 'COMPLETE' : 'INCOMPLETE'}`);

        if (isCompleted) {
          completedWeight += field.weight;
        }
      } else {
        const fieldValue = formData[field.name];

        // Handle different field types
        if (fieldValue !== null && fieldValue !== undefined) {
          if (typeof fieldValue === 'string') {
            isCompleted = fieldValue.trim() !== '';
          } else if (fieldValue instanceof File) {
            isCompleted = true; // File is present
          } else {
            isCompleted = true; // Other types (boolean, number, etc.)
          }
        } else {
          isCompleted = false;
        }

        // console.log(`  ${field.name} (weight: ${field.weight}): ${isCompleted ? 'COMPLETE' : 'INCOMPLETE'} - Value:`, fieldValue);

        if (isCompleted) {
          completedWeight += field.weight;
        }
      }
    });
  });

  // console.log(`\nTotal Weight: ${totalWeight}`);
  // console.log(`Completed Weight: ${completedWeight}`);

  const percentage = (completedWeight / totalWeight) * 100;
  const finalPercentage = Math.round(percentage);

  // console.log(`Raw Percentage: ${percentage}%`);
  // console.log(`Final Percentage: ${finalPercentage}%`);
  // console.log('=== END DEBUG ===\n');

  return finalPercentage;
};

// Handle input changes
export const handleInputChange = (e, setFormData, setErrors, errors) => {
  const { name, value, type, checked, files } = e.target;
  setFormData((prev) => ({
    ...prev,
    [name]: type === 'checkbox' ? checked : type === 'file' ? files[0] : value,
  }));

  if (errors[name]) {
    setErrors((prev) => ({
      ...prev,
      [name]: '',
    }));
  }
};

// Handle degree changes
export const handleDegreeChange = (index, field, value, setFormData) => {
  setFormData((prev) => ({
    ...prev,
    degrees: prev.degrees.map((degree, i) => (i === index ? { ...degree, [field]: value } : degree)),
  }));
};

// Add new degree
export const addDegree = (setFormData) => {
  setFormData((prev) => ({
    ...prev,
    degrees: [...prev.degrees, { degree: '', institution: '', yearOfPassing: '', grade: '' }],
  }));
  notify.success('New degree field added successfully!');
};

// Remove degree
export const removeDegree = (index, formData, setFormData) => {
  if (formData.degrees.length > 1) {
    setFormData((prev) => ({
      ...prev,
      degrees: prev.degrees.filter((_, i) => i !== index),
    }));
    notify.info('Degree field removed successfully!');
  } else {
    notify.warning('At least one degree field is required!');
  }
};

// Validate current step
export const validateCurrentStep = (currentStep, formData, setErrors) => {
  const newErrors = {};

  // Validate required fields based on current step
  if (currentStep === 1) {
    // Basic Info validations
    if (!formData.firstName?.trim()) {
      newErrors.firstName = 'First name is required';
    }
    if (!formData.phoneNumber?.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    }
    if (!formData.gender?.trim()) {
      newErrors.gender = 'Gender is required';
    }
    if (!formData.address?.trim()) {
      newErrors.address = 'Address is required';
    }
    if (!formData.dob?.trim()) {
      newErrors.dob = 'Date of Birth is required';
    }
  }
  else if (currentStep === 2) {
    // Posting Details validations
    if (!formData.department?.trim()) {
      newErrors.department = 'Department is required';
    }
    if (!formData.position?.trim()) {
      newErrors.position = 'Position is required';
    }
  }
   else if (currentStep === 3) {
    // Bank Details validations
    if (!formData.bankName?.trim()) {
      newErrors.bankName = 'Bank name is required';
    }
    if (!formData.accountNumber?.trim()) {
      newErrors.accountNumber = 'Account number is required';
    }
    if (!formData.ifscCode?.trim()) {
      newErrors.ifscCode = 'IFSC code is required';
    }
    if (!formData.branchName?.trim()) {
      newErrors.branchName = 'Branch name is required';
    }
    if (!formData.cancelledCheque) {
      newErrors.cancelledCheque = 'Cancelled cheque is required';
    }
  } 
  else if (currentStep === 4) {
    // Academic Details validations
    const hasCompleteDegree = formData.degrees.some((degree) => {
      const degreeFields = ['degree', 'institution', 'yearOfPassing', 'grade'];
      return degreeFields.every((fieldName) =>
        degree[fieldName] && degree[fieldName].toString().trim() !== ''
      );
    });
    if (!hasCompleteDegree) {
      newErrors.degrees = 'At least one complete degree is required';
    }
  } 
  

  setErrors(newErrors);
  const isValid = Object.keys(newErrors).length === 0;

  if (!isValid) {
    notify.error('Please fill in all required fields!');
  }

  return isValid;
};

// Handle next step
export const nextStep = (currentStep, formData, setErrors, setCurrentStep) => {
  if (validateCurrentStep(currentStep, formData, setErrors)) {
    setCurrentStep((prev) => Math.min(prev + 1, 4));
    notify.success(`Step ${currentStep} completed successfully!`);
  }
};

// Handle previous step
export const prevStep = (setCurrentStep) => {
  setCurrentStep((prev) => Math.max(prev - 1, 1));
};

// Handle form submission
export const handleSubmit = async (e, currentStep, formData, setErrors, setCurrentStep, setIsSubmitting, setFormData) => {
  e.preventDefault();

  if (!validateCurrentStep(currentStep, formData, setErrors)) return;

  // If not on final step, just go to the next step
  if (currentStep < 4) {
    nextStep(currentStep, formData, setErrors, setCurrentStep);
    return;
  }

  // Final step submission
  setIsSubmitting(true);
  notify.loading('Submitting employee information...');

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    // console.log('Employee onboarding completed:', formData);
    notify.success('Employee registered successfully!');

    // Optional: Reset form or redirect
    setFormData(initialFormData);
    setCurrentStep(1);

  } catch (error) {
    // console.error('Submission error:', error);
    notify.error('Failed to submit employee information. Please try again.');
  } finally {
    setIsSubmitting(false);
  }
};