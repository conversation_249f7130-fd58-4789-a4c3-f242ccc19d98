﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using EMS.Services;
using Microsoft.AspNetCore.Identity.Data;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LoginController : ControllerBase
    {
        private readonly EMSDbContext _context;
        private readonly IConfiguration _configuration;

        public LoginController(EMSDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<Employee>> LoginCred(LoginCredential credential)
        {
            var EmpAuth = (from emp in _context.EmpCreds
                           join e in _context.Employees on emp.EmpID equals e.EmpID
                           where emp.UserName == credential.UserName
                           select new {emp,e }).FirstOrDefault();
            if (EmpAuth == null)
            {
                return NotFound("Employee not found");
            }

            if(!EmpAuth.e.IsActive)
            {
                return Unauthorized("Employee is not active");
            }

            if(EmpAuth.emp.Password != credential.Password)
            {
                return Unauthorized("Invalid password");
            }

            var token = GenerateToken(EmpAuth.e, EmpAuth.emp);

            return Ok(new
            {
                token = token,
                empID = EmpAuth.e.EmpID,
                isAutoGenPass = EmpAuth.emp.IsAutoGenerated
            });
        }

        [HttpPut("Forgotpassword")]
        public IActionResult ResetPassword([FromBody] ForgetPasswordModel user)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var users = _context.EmpCreds.FirstOrDefault(u => u.UserName == user.UserName);

            if (users == null)
            {
                return NotFound("No Employee with this username found");
            }

            var emp = _context.Employees.FirstOrDefault(i => i.EmpID == users.EmpID);

            if (emp == null)
            {
                return NotFound("Employee is either inactive or not found");
            }

            string newPassword = PasswordGenerate.GeneratePassword();

          
            users.IsAutoGenerated = true;
            users.Password = newPassword;
            _context.SaveChanges();
            EmailService emailService = new EmailService(_context, _configuration);
            string subject = "Password Reset";
            string result = string.Empty;
            if (!string.IsNullOrEmpty(emp.WorkEmail))
            {
                result = emailService.SendLoginCredentialEmail(emp.WorkEmail, subject, newPassword); //function overload used check emailservices for the 3 parameter function
            }
            else if (!string.IsNullOrEmpty(emp.PersonalEmail))
            {
                result = emailService.SendLoginCredentialEmail(emp.PersonalEmail, subject, newPassword); //function overload used check emailservices for the 3 parameter function
            }
            else
            {
                return Ok(new { password = newPassword, message = "No email found for this employee" });
            }
            
            return Ok(new { result });
        }

        private string GenerateToken(Employee emp, EmpCred ec)
        {
            var securitykey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var credentials = new SigningCredentials(securitykey, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, emp.EmpID.ToString()), // Assuming UserID is the unique identifier
                new Claim("AutoGenPass", ec.IsAutoGenerated.ToString()), // Convert bool to string
            };

            var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Issuer"],
            claims: claims,
           expires: DateTime.Now.AddMinutes(Convert.ToDouble(_configuration["Jwt:ExpireTime"])),
            signingCredentials: credentials
        );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }

    public class LoginCredential
    {
        public string? UserName { get; set; }
        public string? Password { get; set; }
    }

    public class  ForgetPasswordModel
    {
        public string UserName { get; set; }
    }
}
