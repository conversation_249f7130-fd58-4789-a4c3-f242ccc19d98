import React, { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import TableService from '@/services/TableService';
import { confirm } from '@/services/ConfirmationService';
import notification from '@/services/NotificationService';
import { Building, Plus, Edit, Trash2, Users } from 'lucide-react';

const AllDepartments = () => {
  // Global search state
  const [globalFilter, setGlobalFilter] = useState('');
  
  // Local state for departments data to handle updates
  const [departments, setDepartments] = useState([
    {
      _id: '1',
      deptName: 'Human Resources',
      employeeCount: 12,
      positions:['HR Generalist','Recruiter','HR Manager','Training Specialist'],
      createdAt: '2023-01-15T10:30:00Z',
      updatedAt: '2023-01-15T10:30:00Z'
    },
    {
      _id: '2',
      deptName: 'Engineering',
      positions:['Software Engineer','Senior Developer','DevOps Engineer','QA Engineer'],
      employeeCount: 45,
      createdAt: '2023-01-20T14:45:00Z',
      updatedAt: '2023-06-10T09:20:00Z'
    },
    {
      _id: '3',
      deptName: 'Marketing',
      positions:['Marketing Manager','Content Creator','SEO Specialist','Brand Manager'],
      employeeCount: 18,
      createdAt: '2023-02-01T11:15:00Z',
      updatedAt: '2023-02-01T11:15:00Z'
    },
    {
      _id: '4',
      deptName: 'Finance',
      positions:['Accountant','Junior Analyst','Senior Analyst','Finance Manager'],
      employeeCount: 8,
      createdAt: '2023-02-10T16:30:00Z',
      updatedAt: '2023-05-15T13:45:00Z'
    },
    {
      _id: '5',
      deptName: 'Sales',
      positions:['Sales Representative','Account Manager','Business Development','Sales Manager'],
      employeeCount: 22,
      createdAt: '2023-02-15T09:00:00Z',
      updatedAt: '2023-02-15T09:00:00Z'
    },
    {
      _id: '6',
      deptName: 'Operations',
      positions:['Operations Executive','Operations Manager','Operations Analyst','Operations Specialist'],
      employeeCount: 15,
      createdAt: '2023-03-01T12:20:00Z',
      updatedAt: '2023-07-20T10:10:00Z'
    },
    {
      _id: '7',
      deptName: 'Customer Support',
      positions:['Customer Support Representative','Customer Support Manager','Customer Support Analyst','Customer Support Specialist'],
      employeeCount: 10,
      createdAt: '2023-03-10T14:00:00Z',
      updatedAt: '2023-03-10T14:00:00Z'
    },
    {
      _id: '8',
      deptName: 'Design',
      positions:['Graphic Designer','UI/UX Designer','Product Designer','Visual Designer'],
      employeeCount: 6,
      createdAt: '2023-03-15T15:30:00Z',
      updatedAt: '2023-08-05T11:25:00Z'
    }
  ]);

  // Handle department deletion
  const handleDeleteDepartment = async (department) => {
    const confirmed = await confirm({
      title: "Delete Department",
      message: `Are you sure you want to delete the "${department.deptName}" department? This action cannot be undone and may affect ${department.employeeCount} employee${department.employeeCount !== 1 ? 's' : ''}.`,
    });

    if (confirmed) {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Remove department from state
        setDepartments(prev => prev.filter(dept => dept._id !== department._id));
        
        notification().success(`Department "${department.deptName}" deleted successfully!`);
      } catch (error) {
        notification().error('Failed to delete department. Please try again.');
      }
    } else {
      notification().info('Department deletion cancelled');
    }
  };

  // Table columns configuration
  const columns = useMemo(() => [
    {
      accessorKey: 'deptName',
      header: 'Department Name',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Building className="text-blue-600" size={16} />
          </div>
          <div>
            <span className="font-medium text-gray-900">{row.original.deptName}</span>
          </div>
        </div>
      ),
      size: 200,
    },
    {
      accessorKey: 'employeeCount',
      header: 'Employee Count',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Users className="text-gray-500" size={16} />
          <span className="font-medium text-gray-900">{row.original.employeeCount}</span>
          <span className="text-sm text-gray-500">
            employee{row.original.employeeCount !== 1 ? 's' : ''}
          </span>
        </div>
      ),
      size: 150,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created Date',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return (
          <span className="text-gray-700">
            {date.toLocaleDateString('en-IN', { 
              year: 'numeric', 
              month: 'short', 
              day: 'numeric' 
            })}
          </span>
        );
      },
      size: 130,
    },
    {
      accessorKey: 'updatedAt',
      header: 'Last Updated',
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => {
        const date = new Date(row.original.updatedAt);
        const isUpdated = row.original.updatedAt !== row.original.createdAt;
        return (
          <span className={`${isUpdated ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
            {date.toLocaleDateString('en-IN', { 
              year: 'numeric', 
              month: 'short', 
              day: 'numeric' 
            })}
          </span>
        );
      },
      size: 130,
    },
    {
      accessorKey: 'actions',
      header: 'Actions',
      enableSorting: false,
      enableColumnFilter: false,
      cell: ({ row }) => {
        const department = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Link
              to={`/dashboard/departments/edit/${department._id}`}
              className="inline-flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors duration-200"
              title={`Edit ${department.deptName}`}
            >
              <Edit size={14} />
              Edit
            </Link>
            <button
              onClick={() => handleDeleteDepartment(department)}
              className="inline-flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors duration-200"
              title={`Delete ${department.deptName}`}
            >
              <Trash2 size={14} />
              Delete
            </button>
          </div>
        );
      },
      size: 140,
    },
  ], []);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalDepartments = departments.length;
    const totalEmployees = departments.reduce((sum, dept) => sum + dept.employeeCount, 0);
    const totalPositions = departments.reduce((sum, dept) => sum + dept.positions.length, 0);
    const avgEmployeesPerDept = totalDepartments > 0 ? Math.round(totalEmployees / totalDepartments) : 0;
    const largestDept = departments.reduce((max, dept) => 
      dept.employeeCount > (max?.employeeCount || 0) ? dept : max, null
    );
    
    return { totalDepartments, totalEmployees, totalPositions, avgEmployeesPerDept, largestDept };
  }, [departments]);

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Departments</h1>
            <p className="text-gray-600">Manage and organize your company departments</p>
          </div>
          <Link
            to="/dashboard/departments/add"
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} />
            Add Department
          </Link>
        </div>
        
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Total Departments</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.totalDepartments}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Total Positions</h3>
            <p className="text-2xl font-bold text-orange-600">{stats.totalPositions}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Total Employees</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.totalEmployees}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Avg. per Department</h3>
            <p className="text-2xl font-bold text-green-600">{stats.avgEmployeesPerDept}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-sm font-medium text-gray-500">Largest Department</h3>
            <p className="text-lg font-bold text-purple-600">
              {stats.largestDept ? stats.largestDept.deptName : 'N/A'}
            </p>
            {stats.largestDept && (
              <p className="text-sm text-gray-500">{stats.largestDept.employeeCount} employees</p>
            )}
          </div>
        </div>
      </div>

      <div className="">
        <TableService
          columns={columns}
          data={departments}
          initialPageSize={10}
          globalFilter={globalFilter}
          onGlobalFilterChange={setGlobalFilter}
        />
      </div>
    </div>
  );
};

export default AllDepartments;