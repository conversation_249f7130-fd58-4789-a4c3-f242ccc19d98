﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpProfDetailsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpProfDetailsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpProfDetails
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpProfDetail>>> GetEmpProfDetails()
        {
            return await _context.EmpProfDetails.ToListAsync();
        }

        // GET: api/EmpProfDetails/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpProfDetail>> GetEmpProfDetail(int id)
        {
            var empProfDetail = await _context.EmpProfDetails.FindAsync(id);

            if (empProfDetail == null)
            {
                return NotFound();
            }

            return empProfDetail;
        }

        // PUT: api/EmpProfDetails/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpProfDetail(int id, EmpProfDetail empProfDetail)
        {
            if (id != empProfDetail.EpdID)
            {
                return BadRequest();
            }

            _context.Entry(empProfDetail).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpProfDetailExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpProfDetails
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpProfDetail>> PostEmpProfDetail(EmpProfDetail empProfDetail)
        {
            _context.EmpProfDetails.Add(empProfDetail);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpProfDetail", new { id = empProfDetail.EpdID }, empProfDetail);
        }

        // DELETE: api/EmpProfDetails/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpProfDetail(int id)
        {
            var empProfDetail = await _context.EmpProfDetails.FindAsync(id);
            if (empProfDetail == null)
            {
                return NotFound();
            }

            _context.EmpProfDetails.Remove(empProfDetail);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpProfDetailExists(int id)
        {
            return _context.EmpProfDetails.Any(e => e.EpdID == id);
        }
    }
}
