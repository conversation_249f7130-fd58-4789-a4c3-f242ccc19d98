import React, { useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Mail, Phone, MapPin, Calendar, User, Building, GraduationCap, CreditCard } from 'lucide-react';

const EmployeeDetails = () => {
  const { id } = useParams();

  // Hard-coded detailed employee data
  const employeeDetails = useMemo(() => ({
    1: {
      // Basic Info
      firstName: '<PERSON><PERSON>',
      middleName: '<PERSON>',
      lastName: '',
      personalEmail: '<EMAIL>',
      workEmail: '<EMAIL>',
      phoneNumber: '+91-**********',
      address: '12 MG Road, Bengaluru, Karnataka - 560001',
      emergencyContact: 'Sunita Devi',
      emergencyContactNumber: '+91-**********',
      dob: '1990-05-15',
      gender: 'Male',
      doj: '2020-03-15',
      profilePicture: null,

      // Bank Info
      bankName: 'State Bank of India',
      accountNumber: '************',
      ifscCode: 'SBIN0001234',
      branchName: 'MG Road Branch',
      cancelledCheque: null,

      // Academic Info
      degrees: [
        { degree: 'B.Tech in Computer Science', institution: 'IIT Bombay', yearOfPassing: '2012', grade: 'A' },
        { degree: 'M.Tech in Software Engineering', institution: 'IISC Bangalore', yearOfPassing: '2014', grade: 'A+' }
      ],

      // Posting
      department: 'Engineering',
      position: 'Senior Developer',
      empID: 'EMP001'
    },

    2: {
      firstName: 'Priya',
      middleName: '',
      lastName: 'Sharma',
      personalEmail: '<EMAIL>',
      workEmail: '<EMAIL>',
      phoneNumber: '+91-**********',
      address: '98 Lodhi Road, New Delhi - 110003',
      emergencyContact: 'Rakesh Sharma',
      emergencyContactNumber: '+91-**********',
      dob: '1988-12-22',
      gender: 'Female',
      doj: '2019-08-20',
      profilePicture: null,

      // Bank Info
      bankName: 'HDFC Bank',
      accountNumber: '************',
      ifscCode: 'HDFC0004321',
      branchName: 'South Delhi Branch',
      cancelledCheque: null,

      // Academic Info
      degrees: [
        { degree: 'BBA in Marketing', institution: 'Delhi University', yearOfPassing: '2009', grade: 'B+' },
        { degree: 'MBA in Digital Marketing', institution: 'IIM Ahmedabad', yearOfPassing: '2012', grade: 'A' }
      ],

      // Posting
      department: 'Marketing',
      position: 'Marketing Manager',
      empID: 'EMP002'
    }
  }), []);

  const employee = employeeDetails[id];

  if (!employee) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Employee Not Found</h2>
          <p className="text-gray-600 mb-4">The employee with ID {id} could not be found.</p>
          <Link
            to="/dashboard/all-employees"
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft size={16} />
            Back to All Employees
          </Link>
        </div>
      </div>
    );
  }

  const InfoCard = ({ title, icon: Icon, children, className = "" }) => (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Icon className="text-blue-600" size={20} />
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  );

  const InfoRow = ({ label, value }) => (
    <div className="flex flex-col sm:flex-row sm:justify-between py-2 border-b border-gray-100 last:border-b-0">
      <span className="font-medium text-gray-700 mb-1 sm:mb-0">{label}:</span>
      <span className="text-gray-600">{value || 'Not provided'}</span>
    </div>
  );

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <Link
          to="/dashboard/all-employees"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 mb-4 transition-colors"
        >
          <ArrowLeft size={16} />
          Back to All Employees
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">
          {employee.firstName} {employee.middleName} {employee.lastName}
        </h1>
        <p className="text-gray-600">{employee.empID} • {employee.position} • {employee.department}</p>
      </div>

      {/* Employee Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Basic Information */}
        <InfoCard title="Basic Information" icon={User} className="lg:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8">
            <div className="space-y-2">
              <InfoRow label="First Name" value={employee.firstName} />
              <InfoRow label="Middle Name" value={employee.middleName} />
              <InfoRow label="Last Name" value={employee.lastName} />
              <InfoRow label="Personal Email" value={employee.personalEmail} />
              <InfoRow label="Work Email" value={employee.workEmail} />
              <InfoRow label="Phone Number" value={employee.phoneNumber} />
            </div>
            <div className="space-y-2">
              <InfoRow label="Address" value={employee.address} />
              <InfoRow label="Emergency Contact" value={employee.emergencyContact} />
              <InfoRow label="Emergency Phone" value={employee.emergencyContactNumber} />
              <InfoRow label="Date of Birth" value={new Date(employee.dob).toLocaleDateString()} />
              <InfoRow label="Gender" value={employee.gender} />
              <InfoRow label="Date of Joining" value={new Date(employee.doj).toLocaleDateString()} />
            </div>
          </div>
        </InfoCard>

        {/* Bank Information */}
        <InfoCard title="Bank Information" icon={CreditCard}>
          <div className="space-y-2">
            <InfoRow label="Bank Name" value={employee.bankName} />
            <InfoRow label="Account Number" value={employee.accountNumber} />
            <InfoRow label="IFSC Code" value={employee.ifscCode} />
            <InfoRow label="Branch Name" value={employee.branchName} />
            <InfoRow label="Cancelled Cheque" value={employee.cancelledCheque ? 'Uploaded' : 'Not uploaded'} />
          </div>
        </InfoCard>

        {/* Posting Information */}
        <InfoCard title="Posting Information" icon={Building}>
          <div className="space-y-2">
            <InfoRow label="Department" value={employee.department} />
            <InfoRow label="Position" value={employee.position} />
            <InfoRow label="Employee ID" value={employee.empID} />
          </div>
        </InfoCard>

        {/* Academic Information */}
        <InfoCard title="Academic Information" icon={GraduationCap} className="lg:col-span-2">
          <div className="space-y-4">
            {employee.degrees.map((degree, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InfoRow label="Degree" value={degree.degree} />
                  <InfoRow label="Institution" value={degree.institution} />
                  <InfoRow label="Year of Passing" value={degree.yearOfPassing} />
                  <InfoRow label="Grade" value={degree.grade} />
                </div>
              </div>
            ))}
          </div>
        </InfoCard>
      </div>
    </div>
  );
};

export default EmployeeDetails;