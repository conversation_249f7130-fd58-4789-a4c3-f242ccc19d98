/**
 * @fileOverview This file provides a utility for making HTTP requests to the backend API.
 * It uses axios to handle requests and provides a centralized configuration for the API.
 *
 * @uses axios - For making HTTP requests.
 */
import axios from 'axios';
import getBaseURL from '@/utils/getBaseURL';

const baseURL = getBaseURL();
console.log('API Base URL:', baseURL)
// const baseURL = import.meta.env.VITE_API_URL;

const API = axios.create({
  baseURL,
  timeout: 600000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);


export default API;
