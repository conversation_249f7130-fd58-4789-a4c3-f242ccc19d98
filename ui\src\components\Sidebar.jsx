import React, { useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FiLogOut } from "react-icons/fi";
import { FaHome } from "react-icons/fa";
import { NavLink } from "react-router-dom";
import useAuthStore from "../store/authStore";
import { useNavigate } from "react-router-dom";
import { confirm } from "@/services/ConfirmationService";
import notification from "@/services/NotificationService";

const Sidebar = ({ sidebarOpen, setSidebarOpen, menuItems }) => {
  const [isMobile, setIsMobile] = React.useState(false);
  const logout = useAuthStore((state) => state.logout);
  const user = useAuthStore((state) => state.user);
  const navigate = useNavigate();
   const sidebarRef = useRef(null);

  const handleLogout = async () => {
    const confirmed = await confirm({
      title: "Confirm Logout",
      message: "Are you sure you want to log out of your account?",
    });

    if (confirmed) {
      notification().success("Logged out");
      // Perform logout logic here (e.g. clear tokens, redirect, etc.)
      logout();
      navigate("/login");
    }
  };


   // Close on click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target) &&
        sidebarOpen &&
        isMobile
      ) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [sidebarOpen, isMobile]);


  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 1024);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <motion.div
     ref={sidebarRef}
      initial={false}
      animate={{
        x: isMobile ? (sidebarOpen ? 0 : -280) : 0,
        opacity: isMobile ? (sidebarOpen ? 1 : 0) : 1,
      }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className={`fixed inset-y-0 left-0 z-50 w-72 bg-white shadow-2xl border-r border-gray-200/80
        lg:relative lg:translate-x-0 lg:opacity-100 lg:shadow-lg
        ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        }`}
    >
      {/* Header */}
      <div
        className="flex items-center justify-center h-16 bg-gradient-to-r from-gray-600 to-gray-700 border-b border-gray-500/20 cursor-pointer gap-2 "
        onClick={() => navigate("/dashboard")}
      >
        <span className="text-white">
          <FaHome size={25} />
        </span>
        <motion.h1
          className="text-white text-xl font-bold tracking-wide "
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          CUPL | EMS
        </motion.h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-2 overflow-y-auto h-[calc(100vh-8rem)]">
        <AnimatePresence>
          {menuItems.map((item, index) => {
            if (item.type === "heading") {
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="px- py- mt-3 first:mt-0"
                >
                  <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    {item.name}
                  </h3>
                </motion.div>
              );
            }

            return (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <NavLink
                  to={`/dashboard/${item.id}`}
                  onClick={() => isMobile && setSidebarOpen(false)}
                  className={({ isActive }) =>
                    `w-full flex items-center px-5 py-3 rounded-md text-left text-sm font-medium transition-all duration-200 group ${
                      isActive
                        ? "bg-gradient-to-r from-gray-50 to-gray-100 border-l-4 border-gray-600 text-gray-700 font-medium shadow-sm"
                        : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    }`
                  }
                >
                  <div className="mr-3 text-lg text-gray-400 group-hover:text-gray-600">
                    {item.icon}
                  </div>
                  <span>{item.name}</span>
                </NavLink>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </nav>

      {/* Bottom User Info */}
      <div className="p-0 border-t border-gray-200">
        <div className="flex items-center justify-between bg-gray-50 rounded-lg px-1 py-3">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
              <i className="fas fa-user text-white text-sm"></i>
            </div>
            <div className="flex flex-col">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.email || "Admin User"}
              </p>
              <p className="text-xs text-gray-500">System Administrator</p>
            </div>
          </div>
          <motion.button
            onClick={handleLogout}
            className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50 px-3 py-1.5 rounded-md transition-all duration-200 group cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FiLogOut size={25} />
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default Sidebar;
