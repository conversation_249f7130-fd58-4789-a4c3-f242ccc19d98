// src/services/createDepartment.js
import API from '@/services/API';

/**
 * Creates a new department and optionally creates positions for it
 * @param {Object} departmentData - The department data
 * @param {string} departmentData.deptName - The department name
 * @param {Array<string>} departmentData.positions - Array of position names (optional)
 * @returns {Promise<Object>} Promise that resolves to the created department data
 * @throws {Error} Throws error if department creation fails
 */
const createDepartments = async (departmentData) => {
  try {
    const { deptName, positions = [] } = departmentData;

    // Step 1: Create the department first
    const departmentPayload = {
      DeptName: deptName.trim()
    };

    console.log('Creating department with payload:', departmentPayload);
    
    const departmentResponse = await API.post('/Departments', departmentPayload);
    
    if (!departmentResponse.data) {
      throw new Error('Failed to create department - no response data');
    }

    const createdDepartment = departmentResponse.data;
    const deptID = createdDepartment.DeptID || createdDepartment.id;

    if (!deptID) {
      throw new Error('Failed to get department ID from response');
    }

    console.log('Department created successfully:', createdDepartment);

    // Step 2: Create positions if any are provided
    const createdPositions = [];
    
    if (positions && positions.length > 0) {
      console.log(`Creating ${positions.length} positions for department ID: ${deptID}`);
      
      // Create positions sequentially to avoid race conditions
      for (let i = 0; i < positions.length; i++) {
        const positionName = positions[i];
        
        const positionPayload = {
          PositionID: 0, // Let the backend assign the ID
          DeptID: deptID,
          PositionName: positionName.trim(),
          ReportsTo: [] // Empty array as default
        };

        try {
          console.log(`Creating position ${i + 1}/${positions.length}:`, positionPayload);
          
          const positionResponse = await API.post('/Positions', positionPayload);
          
          if (positionResponse.data) {
            createdPositions.push(positionResponse.data);
            console.log(`Position "${positionName}" created successfully`);
          }
        } catch (positionError) {
          console.error(`Failed to create position "${positionName}":`, positionError);
          // Continue creating other positions even if one fails
          // You might want to collect these errors and show them to the user
        }
      }
    }

    // Step 3: Return the complete result
    const result = {
      department: createdDepartment,
      positions: createdPositions,
      totalPositionsCreated: createdPositions.length,
      totalPositionsRequested: positions.length
    };

    console.log('Department creation completed:', result);
    return result;

  } catch (error) {
    console.error('Error in createDepartment service:', error);
    
    // Enhanced error handling
    if (error.response) {
      // Server responded with error status
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.message || error.response.data || 'Unknown server error';
      
      throw new Error(`Server error (${statusCode}): ${errorMessage}`);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network error: Unable to connect to server');
    } else {
      // Something else happened
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }
};

/**
 * Validates department data before sending to API
 * @param {Object} departmentData - The department data to validate
 * @returns {Object} Validation result with isValid boolean and errors array
 */
const validateDepartmentData = (departmentData) => {
  const errors = [];
  
  if (!departmentData) {
    errors.push('Department data is required');
    return { isValid: false, errors };
  }

  const { deptName, positions } = departmentData;

  // Validate department name
  if (!deptName || typeof deptName !== 'string' || !deptName.trim()) {
    errors.push('Department name is required');
  } else if (deptName.trim().length < 2) {
    errors.push('Department name must be at least 2 characters long');
  } else if (deptName.trim().length > 50) {
    errors.push('Department name must not exceed 50 characters');
  }

  // Validate positions if provided
  if (positions && Array.isArray(positions)) {
    positions.forEach((position, index) => {
      if (!position || typeof position !== 'string' || !position.trim()) {
        errors.push(`Position ${index + 1} name is required`);
      } else if (position.trim().length < 2) {
        errors.push(`Position ${index + 1} name must be at least 2 characters long`);
      } else if (position.trim().length > 50) {
        errors.push(`Position ${index + 1} name must not exceed 50 characters`);
      }
    });

    // Check for duplicate positions
    const positionNames = positions.map(p => p.trim().toLowerCase());
    const duplicates = positionNames.filter((name, index) => positionNames.indexOf(name) !== index);
    if (duplicates.length > 0) {
      errors.push('Duplicate position names are not allowed');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export { createDepartments, validateDepartmentData };
export default createDepartments;