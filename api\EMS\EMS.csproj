<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.SimpleEmail" Version="4.0.0.13" />
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Microsoft.AspNet.Cors" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNet.Mvc" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.18">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.18">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.7" />
    <PackageReference Include="MySql.Data" Version="9.3.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
  </ItemGroup>

</Project>
