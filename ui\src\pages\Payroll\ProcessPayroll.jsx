import React, { useState } from 'react';
import * as XLSX from 'xlsx';
import { UploadCloud, FileCheck2, FileX, Download, Loader2 } from 'lucide-react';
import API from '@/services/API';

const ProcessPayroll = () => {
  const [parsedData, setParsedData] = useState([]);
  const [loadingUpload, setLoadingUpload] = useState(false);
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [fileName, setFileName] = useState('');
  const [uploadStatus, setUploadStatus] = useState(null); // 'success' | 'error' | null

  const handleDownload = () => {
    setLoadingDownload(true);
    setTimeout(() => {
      const link = document.createElement('a');
      link.href = '/template.xlsx'; // Ensure file exists in /public
      link.download = 'Payroll_Template.xlsx';
      link.click();
      setLoadingDownload(false);
    }, 800); // Simulated delay
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file || !file.name.endsWith('.xlsx')) {
      setUploadStatus('error');
      alert('Please upload a valid Excel (.xlsx) file.');
      return;
    }

    try {
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      setParsedData(jsonData);
      setFileName(file.name);
      setUploadStatus('success');
    } catch (err) {
      console.error('Error reading Excel file:', err);
      alert('Failed to read the Excel file.');
      setUploadStatus('error');
    }
  };

  const handleUpload = () => {
    if (!parsedData.length) {
      alert('No data to upload.');
      return;
    }

    setLoadingUpload(true);

    const monthMap = {
      Jan: 0, Feb: 1, Mar: 2, Apr: 3, May: 4, Jun: 5,
      Jul: 6, Aug: 7, Sep: 8, Oct: 9, Nov: 10, Dec: 11,
    };

    const toISOStringFromCustomDate = (dateStr) => {
      const match = /^(\d{2})-([A-Za-z]{3})-(\d{4})$/.exec(dateStr);
      if (!match) return dateStr;
      const [_, day, monthAbbr, year] = match;
      const month = monthMap[monthAbbr];
      return new Date(Date.UTC(Number(year), month, Number(day))).toISOString();
    };

    const formattedData = parsedData.map((row) => {
      const formattedRow = { ...row };
      for (const key in formattedRow) {
        const value = formattedRow[key];
        if (typeof value === 'string' && /^\d{2}-[A-Za-z]{3}-\d{4}$/.test(value)) {
          formattedRow[key] = toISOStringFromCustomDate(value);
        }
      }
      return formattedRow;
    });

    API.post('/Attendances', formattedData)
      .then((response) => {
        alert('Payroll data uploaded successfully!');
        console.log('Server response:', response.data);
        setUploadStatus('success');
      })
      .catch((error) => {
        console.error('Upload failed:', error);
        alert('Upload failed. Please try again.');
        setUploadStatus('error');
      })
      .finally(() => setLoadingUpload(false));
  };

  return (
    <div className="max-w-3xl mx-auto py-12 px-6 bg-white shadow-xl rounded-lg space-y-8">
      <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
        <UploadCloud className="w-7 h-7 text-blue-600" />
        Import Attendance
      </h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <button
          onClick={handleDownload}
          disabled={loadingDownload}
          className={`flex items-center justify-center gap-2 px-4 py-2 rounded text-white font-semibold transition ${
            loadingDownload ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {loadingDownload ? (
            <>
              <Loader2 className="animate-spin w-4 h-4" />
              Downloading...
            </>
          ) : (
            <>
              <Download className="w-5 h-5" />
              Download Template
            </>
          )}
        </button>

        <label className="flex items-center justify-center gap-2 bg-gray-100 border-2 border-dashed border-gray-300 px-4 py-2 rounded-md cursor-pointer hover:bg-gray-200 transition text-sm text-gray-700">
          <input type="file" accept=".xlsx" onChange={handleFileChange} className="hidden" />
          <UploadCloud className="w-5 h-5 text-gray-600" />
          {fileName ? 'Change File' : 'Upload Excel (.xlsx)'}
        </label>
      </div>

      {fileName && (
        <div className="flex items-center gap-2 text-sm text-gray-700">
          {uploadStatus === 'success' && <FileCheck2 className="text-green-500 w-5 h-5" />}
          {uploadStatus === 'error' && <FileX className="text-red-500 w-5 h-5" />}
          <span>
            {uploadStatus === 'success'
              ? `Loaded file: ${fileName}`
              : uploadStatus === 'error'
              ? 'Error loading file'
              : ''}
          </span>
        </div>
      )}

      <div className="pt-4">
        <button
          onClick={handleUpload}
          disabled={loadingUpload || parsedData.length === 0}
          className={`w-full flex justify-center items-center gap-2 px-6 py-3 text-white font-medium rounded-md transition ${
            loadingUpload || parsedData.length === 0
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          {loadingUpload ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <UploadCloud className="w-5 h-5" />
              Upload Payroll Data
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProcessPayroll;
